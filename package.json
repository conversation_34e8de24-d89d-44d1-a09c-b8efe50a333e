{"name": "streamer-card", "version": "1.0.0", "description": "这是一个使用 Node.js、Express 和 Puppeteer 的项目，通过提供 API 生成和保存带有特定信息的卡片图像。", "main": "index.js", "scripts": {"start": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc"}, "dependencies": {"@types/cors": "^2.8.17", "cors": "^2.8.5", "express": "^4.19.2", "lru-cache": "^10.4.3", "markdown-it": "^14.1.0", "markdown-it-table": "^4.1.1", "nodemon": "^3.1.4", "puppeteer": "22.12.1", "puppeteer-cluster": "0.24.0", "ts-node": "^10.9.2", "typescript": "^5.5.4"}, "engines": {"node": ">=18.0.0"}, "author": "<EMAIL>", "license": "MIT"}